import 'dart:math';

/// 格式化工具类
class FormatUtils {
  // 私有构造函数
  FormatUtils._();

  /// 格式化价格
  static String formatPrice(double price, {String symbol = '¥', int decimals = 2}) {
    return '$symbol${price.toStringAsFixed(decimals)}';
  }

  /// 格式化数字（添加千分位分隔符）
  static String formatNumber(num number, {int decimals = 0}) {
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String result = number.toStringAsFixed(decimals);
    return result.replaceAllMapped(formatter, (Match m) => '${m[1]},');
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes <= 0) return '0 B';
    
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    final i = (log(bytes) / log(1024)).floor();
    final size = bytes / pow(1024, i);
    
    return '${size.toStringAsFixed(i == 0 ? 0 : 1)} ${suffixes[i]}';
  }

  /// 格式化百分比
  static String formatPercentage(double value, {int decimals = 1}) {
    return '${(value * 100).toStringAsFixed(decimals)}%';
  }

  /// 格式化手机号（隐藏中间4位）
  static String formatPhoneNumber(String phone) {
    if (phone.length != 11) return phone;
    return '${phone.substring(0, 3)}****${phone.substring(7)}';
  }

  /// 格式化身份证号（隐藏中间部分）
  static String formatIdCard(String idCard) {
    if (idCard.length < 8) return idCard;
    final start = idCard.substring(0, 4);
    final end = idCard.substring(idCard.length - 4);
    return '$start****$end';
  }

  /// 格式化银行卡号（每4位一组）
  static String formatBankCard(String cardNumber) {
    return cardNumber.replaceAllMapped(
      RegExp(r'(\d{4})(?=\d)'),
      (match) => '${match.group(1)} ',
    );
  }

  /// 格式化距离
  static String formatDistance(double meters) {
    if (meters < 1000) {
      return '${meters.toInt()}m';
    } else {
      return '${(meters / 1000).toStringAsFixed(1)}km';
    }
  }

  /// 格式化时长
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 格式化数量（大数字简化显示）
  static String formatCount(int count) {
    if (count < 1000) {
      return count.toString();
    } else if (count < 10000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else if (count < 100000000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else {
      return '${(count / 100000000).toStringAsFixed(1)}亿';
    }
  }

  /// 格式化评分
  static String formatRating(double rating, {int decimals = 1}) {
    return rating.toStringAsFixed(decimals);
  }

  /// 移除字符串中的HTML标签
  static String removeHtmlTags(String html) {
    return html.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  /// 截断文本
  static String truncateText(String text, int maxLength, {String suffix = '...'}) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}$suffix';
  }

  /// 首字母大写
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// 驼峰命名转下划线
  static String camelToSnake(String camelCase) {
    return camelCase.replaceAllMapped(
      RegExp(r'[A-Z]'),
      (match) => '_${match.group(0)!.toLowerCase()}',
    );
  }

  /// 下划线转驼峰命名
  static String snakeToCamel(String snakeCase) {
    return snakeCase.replaceAllMapped(
      RegExp(r'_([a-z])'),
      (match) => match.group(1)!.toUpperCase(),
    );
  }

  /// 格式化URL参数
  static String formatUrlParams(Map<String, dynamic> params) {
    if (params.isEmpty) return '';
    
    final pairs = params.entries
        .where((entry) => entry.value != null)
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}');
    
    return pairs.join('&');
  }

  /// 解析URL参数
  static Map<String, String> parseUrlParams(String query) {
    final params = <String, String>{};
    
    if (query.isNotEmpty) {
      final pairs = query.split('&');
      for (final pair in pairs) {
        final keyValue = pair.split('=');
        if (keyValue.length == 2) {
          params[keyValue[0]] = Uri.decodeComponent(keyValue[1]);
        }
      }
    }
    
    return params;
  }
}
