import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import 'custom_button.dart';

/// 自定义对话框
class CustomDialog extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final String? content;
  final Widget? contentWidget;
  final List<Widget>? actions;
  final bool barrierDismissible;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? actionsPadding;

  const CustomDialog({
    super.key,
    this.title,
    this.titleWidget,
    this.content,
    this.contentWidget,
    this.actions,
    this.barrierDismissible = true,
    this.contentPadding,
    this.actionsPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 320.w,
          minWidth: 280.w,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            if (title != null || titleWidget != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.fromLTRB(24.w, 24.h, 24.w, 16.h),
                child: titleWidget ??
                    Text(
                      title!,
                      style: AppTextStyles.titleLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
              ),
            ],
            // 内容
            if (content != null || contentWidget != null) ...[
              Container(
                width: double.infinity,
                padding: contentPadding ??
                    EdgeInsets.fromLTRB(
                      24.w,
                      title != null || titleWidget != null ? 0 : 24.h,
                      24.w,
                      24.h,
                    ),
                child: contentWidget ??
                    Text(
                      content!,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
              ),
            ],
            // 操作按钮
            if (actions != null && actions!.isNotEmpty) ...[
              Container(
                padding: actionsPadding ?? EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
                child: Row(
                  children: _buildActions(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActions() {
    if (actions!.length == 1) {
      return [Expanded(child: actions!.first)];
    }

    final List<Widget> actionWidgets = [];
    for (int i = 0; i < actions!.length; i++) {
      if (i > 0) {
        actionWidgets.add(SizedBox(width: 12.w));
      }
      actionWidgets.add(Expanded(child: actions![i]));
    }
    return actionWidgets;
  }

  /// 显示对话框
  static Future<T?> show<T>(
    BuildContext context, {
    String? title,
    Widget? titleWidget,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    bool barrierDismissible = true,
    EdgeInsetsGeometry? contentPadding,
    EdgeInsetsGeometry? actionsPadding,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => CustomDialog(
        title: title,
        titleWidget: titleWidget,
        content: content,
        contentWidget: contentWidget,
        actions: actions,
        barrierDismissible: barrierDismissible,
        contentPadding: contentPadding,
        actionsPadding: actionsPadding,
      ),
    );
  }
}

/// 确认对话框
class ConfirmDialog extends StatelessWidget {
  final String title;
  final String content;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool isDestructive;

  const ConfirmDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText,
    this.cancelText,
    this.onConfirm,
    this.onCancel,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return CustomDialog(
      title: title,
      content: content,
      actions: [
        OutlineButton(
          text: cancelText ?? '取消',
          onPressed: () {
            Navigator.of(context).pop(false);
            onCancel?.call();
          },
          size: ButtonSize.small,
        ),
        CustomButton(
          text: confirmText ?? '确定',
          onPressed: () {
            Navigator.of(context).pop(true);
            onConfirm?.call();
          },
          type: isDestructive ? ButtonType.danger : ButtonType.primary,
          size: ButtonSize.small,
        ),
      ],
    );
  }

  /// 显示确认对话框
  static Future<bool?> show(
    BuildContext context, {
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        isDestructive: isDestructive,
      ),
    );
  }
}

/// 提示对话框
class AlertDialog extends StatelessWidget {
  final String title;
  final String content;
  final String? buttonText;
  final VoidCallback? onPressed;

  const AlertDialog({
    super.key,
    required this.title,
    required this.content,
    this.buttonText,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return CustomDialog(
      title: title,
      content: content,
      actions: [
        PrimaryButton(
          text: buttonText ?? '确定',
          onPressed: () {
            Navigator.of(context).pop();
            onPressed?.call();
          },
          size: ButtonSize.small,
        ),
      ],
    );
  }

  /// 显示提示对话框
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String content,
    String? buttonText,
    VoidCallback? onPressed,
  }) {
    return showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: title,
        content: content,
        buttonText: buttonText,
        onPressed: onPressed,
      ),
    );
  }
}

/// 加载对话框
class LoadingDialog extends StatelessWidget {
  final String? message;

  const LoadingDialog({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 32.w,
              height: 32.w,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
            if (message != null) ...[
              SizedBox(height: 16.h),
              Text(
                message!,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 显示加载对话框
  static void show(
    BuildContext context, {
    String? message,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => LoadingDialog(message: message),
    );
  }

  /// 隐藏加载对话框
  static void hide(BuildContext context) {
    Navigator.of(context).pop();
  }
}

/// 底部选择对话框
class BottomSheetDialog extends StatelessWidget {
  final String? title;
  final List<BottomSheetItem> items;
  final VoidCallback? onCancel;

  const BottomSheetDialog({
    super.key,
    this.title,
    required this.items,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            if (title != null) ...[
              Container(
                padding: EdgeInsets.all(16.w),
                child: Text(
                  title!,
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Divider(height: 1.h, color: AppColors.divider),
            ],
            // 选项列表
            ...items.map((item) => _buildItem(context, item)),
            // 取消按钮
            Container(
              height: 8.h,
              color: AppColors.background,
            ),
            ListTile(
              title: Text(
                '取消',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              onTap: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItem(BuildContext context, BottomSheetItem item) {
    return ListTile(
      leading: item.icon,
      title: Text(
        item.title,
        style: AppTextStyles.bodyLarge.copyWith(
          color: item.isDestructive ? AppColors.error : AppColors.textPrimary,
        ),
      ),
      onTap: () {
        Navigator.of(context).pop(item);
        item.onTap?.call();
      },
    );
  }

  /// 显示底部选择对话框
  static Future<BottomSheetItem?> show(
    BuildContext context, {
    String? title,
    required List<BottomSheetItem> items,
    VoidCallback? onCancel,
  }) {
    return showModalBottomSheet<BottomSheetItem>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => BottomSheetDialog(
        title: title,
        items: items,
        onCancel: onCancel,
      ),
    );
  }
}

/// 底部选择项
class BottomSheetItem {
  final String title;
  final Widget? icon;
  final VoidCallback? onTap;
  final bool isDestructive;

  const BottomSheetItem({
    required this.title,
    this.icon,
    this.onTap,
    this.isDestructive = false,
  });
}
