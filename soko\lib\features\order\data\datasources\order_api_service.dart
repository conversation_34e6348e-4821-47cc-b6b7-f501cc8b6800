import '../../../../core/api/base_api_service.dart';
import '../../../../core/models/query_params.dart';
import '../../../../core/network/api_response.dart';
import '../../domain/entities/order.dart';

/// 订单API服务
class OrderApiService extends BaseApiService {
  static const String _orderListPath = '/order/list';
  static const String _orderDetailPath = '/order';
  static const String _orderCreatePath = '/order/create';
  static const String _orderCancelPath = '/order/cancel';
  static const String _orderPayPath = '/order/pay';
  static const String _orderConfirmReceivePath = '/order/confirm-receive';
  static const String _orderRefundPath = '/order/refund';
  static const String _orderTrackingPath = '/order/tracking';

  /// 获取订单列表
  Future<ApiResponse<PageResponse<Order>>> getOrderList({
    required OrderQueryParams params,
  }) async {
    final queryParams = buildQueryParams({
      'page': params.page,
      'size': params.size,
      'orderStatus': params.orderStatus,
      'startDate': params.startDate,
      'endDate': params.endDate,
      'orderNo': params.orderNo,
    });

    final response = await get<Map<String, dynamic>>(
      _orderListPath,
      queryParameters: queryParams,
    );

    if (response.isSuccess && response.data != null) {
      final pageData = handlePageResponse<Order>(
        response.data!,
        (json) => Order.fromJson(json),
      );
      
      return ApiResponse<PageResponse<Order>>(
        code: response.code,
        message: response.message,
        data: pageData,
        timestamp: response.timestamp,
      );
    }

    return ApiResponse<PageResponse<Order>>(
      code: response.code,
      message: response.message,
      data: null,
      timestamp: response.timestamp,
    );
  }

  /// 获取订单详情
  Future<ApiResponse<Order>> getOrderDetail({
    required String orderId,
  }) async {
    return await get<Order>(
      '$_orderDetailPath/$orderId',
      fromJson: (data) => Order.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 创建订单
  Future<ApiResponse<CreateOrderResponse>> createOrder({
    required CreateOrderRequest request,
  }) async {
    return await post<CreateOrderResponse>(
      _orderCreatePath,
      data: request.toJson(),
      fromJson: (data) => CreateOrderResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 取消订单
  Future<ApiResponse<void>> cancelOrder({
    required String orderId,
    String? reason,
  }) async {
    return await post<void>(
      '$_orderCancelPath/$orderId',
      data: {
        if (reason != null) 'reason': reason,
      },
    );
  }

  /// 支付订单
  Future<ApiResponse<PaymentResponse>> payOrder({
    required String orderId,
    required String paymentMethod,
    String? couponId,
  }) async {
    return await post<PaymentResponse>(
      '$_orderPayPath/$orderId',
      data: {
        'paymentMethod': paymentMethod,
        if (couponId != null) 'couponId': couponId,
      },
      fromJson: (data) => PaymentResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 确认收货
  Future<ApiResponse<void>> confirmReceive({
    required String orderId,
  }) async {
    return await post<void>(
      '$_orderConfirmReceivePath/$orderId',
    );
  }

  /// 申请退款
  Future<ApiResponse<void>> requestRefund({
    required String orderId,
    required String reason,
    String? description,
    List<String>? images,
  }) async {
    return await post<void>(
      '$_orderRefundPath/$orderId',
      data: {
        'reason': reason,
        if (description != null) 'description': description,
        if (images != null) 'images': images,
      },
    );
  }

  /// 获取物流信息
  Future<ApiResponse<TrackingInfo>> getTrackingInfo({
    required String orderId,
  }) async {
    return await get<TrackingInfo>(
      '$_orderTrackingPath/$orderId',
      fromJson: (data) => TrackingInfo.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取订单统计
  Future<ApiResponse<OrderStats>> getOrderStats() async {
    return await get<OrderStats>(
      '/order/stats',
      fromJson: (data) => OrderStats.fromJson(data as Map<String, dynamic>),
    );
  }
}

/// 创建订单请求
class CreateOrderRequest {
  final List<OrderItemRequest> items;
  final String addressId;
  final String? note;
  final String? couponId;

  const CreateOrderRequest({
    required this.items,
    required this.addressId,
    this.note,
    this.couponId,
  });

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) {
    return CreateOrderRequest(
      items: (json['items'] as List<dynamic>)
          .map((item) => OrderItemRequest.fromJson(item as Map<String, dynamic>))
          .toList(),
      addressId: json['addressId'] as String,
      note: json['note'] as String?,
      couponId: json['couponId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'addressId': addressId,
      if (note != null) 'note': note,
      if (couponId != null) 'couponId': couponId,
    };
  }
}

/// 订单项请求
class OrderItemRequest {
  final String productId;
  final String? skuId;
  final int quantity;

  const OrderItemRequest({
    required this.productId,
    this.skuId,
    required this.quantity,
  });

  factory OrderItemRequest.fromJson(Map<String, dynamic> json) {
    return OrderItemRequest(
      productId: json['productId'] as String,
      skuId: json['skuId'] as String?,
      quantity: json['quantity'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      if (skuId != null) 'skuId': skuId,
      'quantity': quantity,
    };
  }
}

/// 创建订单响应
class CreateOrderResponse {
  final String orderId;
  final String orderNo;
  final double totalAmount;
  final double payAmount;

  const CreateOrderResponse({
    required this.orderId,
    required this.orderNo,
    required this.totalAmount,
    required this.payAmount,
  });

  factory CreateOrderResponse.fromJson(Map<String, dynamic> json) {
    return CreateOrderResponse(
      orderId: json['orderId'] as String,
      orderNo: json['orderNo'] as String,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      payAmount: (json['payAmount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orderId': orderId,
      'orderNo': orderNo,
      'totalAmount': totalAmount,
      'payAmount': payAmount,
    };
  }
}

/// 支付响应
class PaymentResponse {
  final String paymentId;
  final String paymentUrl;
  final String? qrCode;
  final Map<String, dynamic>? paymentData;

  const PaymentResponse({
    required this.paymentId,
    required this.paymentUrl,
    this.qrCode,
    this.paymentData,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      paymentId: json['paymentId'] as String,
      paymentUrl: json['paymentUrl'] as String,
      qrCode: json['qrCode'] as String?,
      paymentData: json['paymentData'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paymentId': paymentId,
      'paymentUrl': paymentUrl,
      if (qrCode != null) 'qrCode': qrCode,
      if (paymentData != null) 'paymentData': paymentData,
    };
  }
}

/// 物流信息
class TrackingInfo {
  final String trackingNo;
  final String company;
  final String status;
  final List<TrackingRecord> records;

  const TrackingInfo({
    required this.trackingNo,
    required this.company,
    required this.status,
    required this.records,
  });

  factory TrackingInfo.fromJson(Map<String, dynamic> json) {
    return TrackingInfo(
      trackingNo: json['trackingNo'] as String,
      company: json['company'] as String,
      status: json['status'] as String,
      records: (json['records'] as List<dynamic>)
          .map((item) => TrackingRecord.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trackingNo': trackingNo,
      'company': company,
      'status': status,
      'records': records.map((item) => item.toJson()).toList(),
    };
  }
}

/// 物流记录
class TrackingRecord {
  final String status;
  final String description;
  final String location;
  final int time;

  const TrackingRecord({
    required this.status,
    required this.description,
    required this.location,
    required this.time,
  });

  factory TrackingRecord.fromJson(Map<String, dynamic> json) {
    return TrackingRecord(
      status: json['status'] as String,
      description: json['description'] as String,
      location: json['location'] as String,
      time: json['time'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'description': description,
      'location': location,
      'time': time,
    };
  }
}

/// 订单统计
class OrderStats {
  final int totalOrders;
  final int pendingOrders;
  final int shippedOrders;
  final int completedOrders;
  final double totalAmount;

  const OrderStats({
    required this.totalOrders,
    required this.pendingOrders,
    required this.shippedOrders,
    required this.completedOrders,
    required this.totalAmount,
  });

  factory OrderStats.fromJson(Map<String, dynamic> json) {
    return OrderStats(
      totalOrders: json['totalOrders'] as int,
      pendingOrders: json['pendingOrders'] as int,
      shippedOrders: json['shippedOrders'] as int,
      completedOrders: json['completedOrders'] as int,
      totalAmount: (json['totalAmount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalOrders': totalOrders,
      'pendingOrders': pendingOrders,
      'shippedOrders': shippedOrders,
      'completedOrders': completedOrders,
      'totalAmount': totalAmount,
    };
  }
}
