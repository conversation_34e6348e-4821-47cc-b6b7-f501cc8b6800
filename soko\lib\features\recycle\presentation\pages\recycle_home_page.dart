import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/presentation/widgets/custom_app_bar.dart';

/// 回收首页
class RecycleHomePage extends ConsumerWidget {
  const RecycleHomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '回收',
        showBackButton: false,
      ),
      body: const Center(
        child: Text('回收页面 - 待实现'),
      ),
    );
  }
}
