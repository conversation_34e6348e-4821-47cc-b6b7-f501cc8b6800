import 'package:dio/dio.dart';

import '../../services/storage_service.dart';

/// 认证拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 获取存储的token
    final token = StorageService.getToken();
    
    if (token != null && token.isNotEmpty) {
      // 添加Authorization头
      options.headers['Authorization'] = 'Bearer $token';
    }
    
    // 添加设备信息等通用头部
    options.headers['User-Agent'] = 'SokoApp/1.0.0';
    options.headers['Platform'] = 'Flutter';
    
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 检查响应中是否有新的token
    final newToken = response.headers.value('new-token');
    if (newToken != null && newToken.isNotEmpty) {
      // 更新存储的token
      StorageService.saveToken(newToken);
    }
    
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理401未授权错误
    if (err.response?.statusCode == 401) {
      // 清除过期的token
      StorageService.removeToken();
      StorageService.removeUserInfo();
      
      // 可以在这里触发跳转到登录页面的逻辑
      // 或者发送一个全局事件通知应用处理登录状态
    }
    
    super.onError(err, handler);
  }
}
