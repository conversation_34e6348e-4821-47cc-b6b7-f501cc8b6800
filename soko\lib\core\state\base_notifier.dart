import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../exceptions/network_exception.dart';
import '../network/api_response.dart';
import 'base_state.dart';

/// 基础状态管理器
abstract class BaseNotifier<T> extends StateNotifier<BaseState<T>> {
  BaseNotifier() : super(const BaseState.initial());

  /// 执行异步操作
  Future<void> execute(Future<T> Function() operation) async {
    try {
      state = const BaseState.loading();
      final result = await operation();
      state = BaseState.success(result);
    } catch (error) {
      _handleError(error);
    }
  }

  /// 处理错误
  void _handleError(dynamic error) {
    String message = '操作失败';
    String? code;

    if (error is NetworkException) {
      message = error.userFriendlyMessage;
      code = error.code;
    } else if (error is Exception) {
      message = error.toString();
    } else {
      message = error.toString();
    }

    state = BaseState.error(message, code: code);
  }

  /// 重置状态
  void reset() {
    state = const BaseState.initial();
  }

  /// 设置成功状态
  void setSuccess(T data) {
    state = BaseState.success(data);
  }

  /// 设置错误状态
  void setError(String message, {String? code}) {
    state = BaseState.error(message, code: code);
  }
}

/// 分页状态管理器
abstract class PageNotifier<T> extends StateNotifier<PageState<T>> {
  PageNotifier() : super(PageState.initial());

  /// 刷新数据（重新加载第一页）
  Future<void> refresh() async {
    state = state.copyWith(
      isLoading: true,
      error: null,
      currentPage: 0,
    );

    try {
      final response = await loadPage(1);
      state = PageState(
        items: response.list,
        isLoading: false,
        hasMore: response.hasNext,
        currentPage: response.page,
        totalPages: response.totalPages,
        totalCount: response.total,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(error),
      );
    }
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (!state.canLoadMore) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final response = await loadPage(nextPage);
      
      state = state.copyWith(
        items: [...state.items, ...response.list],
        isLoading: false,
        hasMore: response.hasNext,
        currentPage: response.page,
        totalPages: response.totalPages,
        totalCount: response.total,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(error),
      );
    }
  }

  /// 子类需要实现的加载页面方法
  Future<PageResponse<T>> loadPage(int page);

  /// 获取错误消息
  String _getErrorMessage(dynamic error) {
    if (error is NetworkException) {
      return error.userFriendlyMessage;
    }
    return error.toString();
  }

  /// 重置状态
  void reset() {
    state = PageState.initial();
  }

  /// 添加项目
  void addItem(T item) {
    state = state.copyWith(
      items: [...state.items, item],
      totalCount: state.totalCount + 1,
    );
  }

  /// 更新项目
  void updateItem(T item, bool Function(T) predicate) {
    final items = state.items.map((e) => predicate(e) ? item : e).toList();
    state = state.copyWith(items: items);
  }

  /// 删除项目
  void removeItem(bool Function(T) predicate) {
    final items = state.items.where((e) => !predicate(e)).toList();
    state = state.copyWith(
      items: items,
      totalCount: state.totalCount - 1,
    );
  }
}

/// 异步操作状态管理器
abstract class AsyncNotifier<T> extends StateNotifier<AsyncState<T>> {
  AsyncNotifier() : super(const AsyncState.idle());

  /// 执行异步操作
  Future<void> execute(Future<T> Function() operation) async {
    try {
      state = const AsyncState.loading();
      final result = await operation();
      state = AsyncState.success(result);
    } catch (error) {
      state = AsyncState.error(_getErrorMessage(error));
    }
  }

  /// 获取错误消息
  String _getErrorMessage(dynamic error) {
    if (error is NetworkException) {
      return error.userFriendlyMessage;
    }
    return error.toString();
  }

  /// 重置状态
  void reset() {
    state = const AsyncState.idle();
  }

  /// 设置成功状态
  void setSuccess(T data) {
    state = AsyncState.success(data);
  }

  /// 设置错误状态
  void setError(String message) {
    state = AsyncState.error(message);
  }
}
