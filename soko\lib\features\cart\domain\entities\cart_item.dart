import 'package:json_annotation/json_annotation.dart';

part 'cart_item.g.dart';

/// 购物车项实体类
@JsonSerializable()
class CartItem {
  @Json<PERSON>ey(name: 'id')
  final String id;

  @Json<PERSON><PERSON>(name: 'userId')
  final String userId;

  @Json<PERSON><PERSON>(name: 'productId')
  final String productId;

  @Json<PERSON>ey(name: 'productName')
  final String productName;

  @JsonKey(name: 'productImage')
  final String? productImage;

  @<PERSON>son<PERSON>ey(name: 'skuId')
  final String? skuId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'skuName')
  final String? skuName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'price')
  final double price;

  @Json<PERSON>ey(name: 'originalPrice')
  final double? originalPrice;

  @Json<PERSON>ey(name: 'quantity')
  final int quantity;

  @JsonKey(name: 'maxQuantity')
  final int? maxQuantity;

  @<PERSON>son<PERSON>ey(name: 'selected')
  final bool selected;

  @<PERSON>son<PERSON><PERSON>(name: 'available')
  final bool available;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime')
  final int createTime;

  @Json<PERSON><PERSON>(name: 'updateTime')
  final int updateTime;

  const CartItem({
    required this.id,
    required this.userId,
    required this.productId,
    required this.productName,
    this.productImage,
    this.skuId,
    this.skuName,
    required this.price,
    this.originalPrice,
    required this.quantity,
    this.maxQuantity,
    required this.selected,
    required this.available,
    required this.createTime,
    required this.updateTime,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) => _$CartItemFromJson(json);

  Map<String, dynamic> toJson() => _$CartItemToJson(this);

  /// 获取总价
  double get totalPrice => price * quantity;

  /// 获取原始总价
  double? get totalOriginalPrice {
    if (originalPrice == null) return null;
    return originalPrice! * quantity;
  }

  /// 是否有折扣
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  /// 折扣金额
  double get discountAmount {
    if (!hasDiscount) return 0;
    return (originalPrice! - price) * quantity;
  }

  /// 是否可以增加数量
  bool get canIncrease {
    if (!available) return false;
    if (maxQuantity == null) return true;
    return quantity < maxQuantity!;
  }

  /// 是否可以减少数量
  bool get canDecrease {
    return quantity > 1;
  }

  /// 复制并更新购物车项
  CartItem copyWith({
    String? id,
    String? userId,
    String? productId,
    String? productName,
    String? productImage,
    String? skuId,
    String? skuName,
    double? price,
    double? originalPrice,
    int? quantity,
    int? maxQuantity,
    bool? selected,
    bool? available,
    int? createTime,
    int? updateTime,
  }) {
    return CartItem(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productImage: productImage ?? this.productImage,
      skuId: skuId ?? this.skuId,
      skuName: skuName ?? this.skuName,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      quantity: quantity ?? this.quantity,
      maxQuantity: maxQuantity ?? this.maxQuantity,
      selected: selected ?? this.selected,
      available: available ?? this.available,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CartItem(id: $id, productName: $productName, quantity: $quantity, price: $price)';
  }
}

/// 添加到购物车请求
@JsonSerializable()
class AddToCartRequest {
  @JsonKey(name: 'productId')
  final String productId;

  @JsonKey(name: 'skuId')
  final String? skuId;

  @JsonKey(name: 'quantity')
  final int quantity;

  const AddToCartRequest({
    required this.productId,
    this.skuId,
    required this.quantity,
  });

  factory AddToCartRequest.fromJson(Map<String, dynamic> json) =>
      _$AddToCartRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddToCartRequestToJson(this);
}

/// 更新购物车项请求
@JsonSerializable()
class UpdateCartItemRequest {
  @JsonKey(name: 'quantity')
  final int? quantity;

  @JsonKey(name: 'selected')
  final bool? selected;

  const UpdateCartItemRequest({
    this.quantity,
    this.selected,
  });

  factory UpdateCartItemRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateCartItemRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateCartItemRequestToJson(this);
}

/// 购物车统计信息
@JsonSerializable()
class CartSummary {
  @JsonKey(name: 'totalItems')
  final int totalItems;

  @JsonKey(name: 'selectedItems')
  final int selectedItems;

  @JsonKey(name: 'totalAmount')
  final double totalAmount;

  @JsonKey(name: 'totalOriginalAmount')
  final double? totalOriginalAmount;

  @JsonKey(name: 'totalDiscount')
  final double totalDiscount;

  @JsonKey(name: 'freight')
  final double? freight;

  @JsonKey(name: 'finalAmount')
  final double finalAmount;

  const CartSummary({
    required this.totalItems,
    required this.selectedItems,
    required this.totalAmount,
    this.totalOriginalAmount,
    required this.totalDiscount,
    this.freight,
    required this.finalAmount,
  });

  factory CartSummary.fromJson(Map<String, dynamic> json) => _$CartSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$CartSummaryToJson(this);

  /// 是否有折扣
  bool get hasDiscount => totalDiscount > 0;

  /// 是否有运费
  bool get hasFreight => freight != null && freight! > 0;
}
