import '../../../../core/api/base_api_service.dart';
import '../../../../core/models/file_models.dart';
import '../../../../core/models/query_params.dart';
import '../../../../core/network/api_response.dart';
import '../../domain/entities/recycle_order.dart';

/// 回收API服务
class RecycleApiService extends BaseApiService {
  static const String _recycleOrderPath = '/recycling-order';
  static const String _recycleOrderListPath = '/recycling-order/list';
  static const String _recycleOrderDetailPath = '/recycling-order';
  static const String _recycleOrderCreatePath = '/recycling-order/create';
  static const String _recycleOrderUpdatePath = '/recycling-order/update';
  static const String _recycleOrderCancelPath = '/recycling-order/cancel';
  static const String _recycleOrderShipPath = '/recycling-order/ship';
  static const String _recycleOrderCompleteReturnPath = '/recycling-order/complete-return';
  static const String _recycleOrderConfirmPricePath = '/recycling-order/confirm-price';
  static const String _recycleCategoriesPath = '/recycling/categories';
  static const String _recycleConditionsPath = '/recycling/conditions';
  static const String _recycleEstimatePath = '/recycling/estimate';

  /// 获取回收订单列表
  Future<ApiResponse<PageResponse<RecycleOrder>>> getRecycleOrderList({
    required RecycleOrderQueryParams params,
  }) async {
    final queryParams = buildQueryParams({
      'page': params.page,
      'size': params.size,
      'orderStatus': params.orderStatus,
      'productName': params.productName,
      'startDate': params.startDate,
      'endDate': params.endDate,
    });

    final response = await get<Map<String, dynamic>>(
      _recycleOrderListPath,
      queryParameters: queryParams,
    );

    if (response.isSuccess && response.data != null) {
      final pageData = handlePageResponse<RecycleOrder>(
        response.data!,
        (json) => RecycleOrder.fromJson(json),
      );
      
      return ApiResponse<PageResponse<RecycleOrder>>(
        code: response.code,
        message: response.message,
        data: pageData,
        timestamp: response.timestamp,
      );
    }

    return ApiResponse<PageResponse<RecycleOrder>>(
      code: response.code,
      message: response.message,
      data: null,
      timestamp: response.timestamp,
    );
  }

  /// 获取回收订单详情
  Future<ApiResponse<RecycleOrder>> getRecycleOrderDetail({
    required String orderId,
  }) async {
    return await get<RecycleOrder>(
      '$_recycleOrderDetailPath/$orderId',
      fromJson: (data) => RecycleOrder.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 创建回收订单
  Future<ApiResponse<RecycleOrder>> createRecycleOrder({
    required CreateRecycleOrderRequest request,
  }) async {
    return await post<RecycleOrder>(
      _recycleOrderCreatePath,
      data: request.toJson(),
      fromJson: (data) => RecycleOrder.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 更新回收订单
  Future<ApiResponse<RecycleOrder>> updateRecycleOrder({
    required String orderId,
    required Map<String, dynamic> updateData,
  }) async {
    return await put<RecycleOrder>(
      '$_recycleOrderUpdatePath/$orderId',
      data: updateData,
      fromJson: (data) => RecycleOrder.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 取消回收订单
  Future<ApiResponse<void>> cancelRecycleOrder({
    required String orderId,
    String? reason,
  }) async {
    return await post<void>(
      '$_recycleOrderCancelPath/$orderId',
      data: {
        if (reason != null) 'reason': reason,
      },
    );
  }

  /// 确认寄送
  Future<ApiResponse<void>> confirmShipment({
    required String orderId,
    required ShippingInfo shippingInfo,
  }) async {
    return await post<void>(
      '$_recycleOrderShipPath/$orderId',
      data: shippingInfo.toJson(),
    );
  }

  /// 完成退货
  Future<ApiResponse<void>> completeReturn({
    required String orderId,
  }) async {
    return await post<void>(
      '$_recycleOrderCompleteReturnPath/$orderId',
    );
  }

  /// 确认价格
  Future<ApiResponse<void>> confirmPrice({
    required String orderId,
    required bool accept,
    String? reason,
  }) async {
    return await post<void>(
      '$_recycleOrderConfirmPricePath/$orderId',
      data: {
        'accept': accept,
        if (reason != null) 'reason': reason,
      },
    );
  }

  /// 获取回收分类
  Future<ApiResponse<List<CategoryItem>>> getRecycleCategories() async {
    return await get<List<CategoryItem>>(
      _recycleCategoriesPath,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => CategoryItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 获取成色选项
  Future<ApiResponse<List<ConditionOption>>> getConditionOptions() async {
    return await get<List<ConditionOption>>(
      _recycleConditionsPath,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => ConditionOption.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 估价
  Future<ApiResponse<EstimateResponse>> estimatePrice({
    required String category,
    required String condition,
    required String productName,
    String? productModel,
    String? brand,
    List<String>? imageFiles,
  }) async {
    return await post<EstimateResponse>(
      _recycleEstimatePath,
      data: {
        'category': category,
        'condition': condition,
        'productName': productName,
        if (productModel != null) 'productModel': productModel,
        if (brand != null) 'brand': brand,
        if (imageFiles != null) 'imageFiles': imageFiles,
      },
      fromJson: (data) => EstimateResponse.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取回收统计信息
  Future<ApiResponse<RecycleStats>> getRecycleStats() async {
    return await get<RecycleStats>(
      '/recycling/stats',
      fromJson: (data) => RecycleStats.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取回收流程说明
  Future<ApiResponse<List<ProcessStep>>> getRecycleProcess() async {
    return await get<List<ProcessStep>>(
      '/recycling/process',
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => ProcessStep.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// 估价响应
class EstimateResponse {
  final double estimatedPrice;
  final double minPrice;
  final double maxPrice;
  final String? description;
  final List<String>? factors;

  const EstimateResponse({
    required this.estimatedPrice,
    required this.minPrice,
    required this.maxPrice,
    this.description,
    this.factors,
  });

  factory EstimateResponse.fromJson(Map<String, dynamic> json) {
    return EstimateResponse(
      estimatedPrice: (json['estimatedPrice'] as num).toDouble(),
      minPrice: (json['minPrice'] as num).toDouble(),
      maxPrice: (json['maxPrice'] as num).toDouble(),
      description: json['description'] as String?,
      factors: (json['factors'] as List<dynamic>?)?.cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'estimatedPrice': estimatedPrice,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'description': description,
      'factors': factors,
    };
  }
}

/// 回收统计信息
class RecycleStats {
  final int totalOrders;
  final int completedOrders;
  final double totalAmount;
  final int thisMonthOrders;
  final double thisMonthAmount;

  const RecycleStats({
    required this.totalOrders,
    required this.completedOrders,
    required this.totalAmount,
    required this.thisMonthOrders,
    required this.thisMonthAmount,
  });

  factory RecycleStats.fromJson(Map<String, dynamic> json) {
    return RecycleStats(
      totalOrders: json['totalOrders'] as int,
      completedOrders: json['completedOrders'] as int,
      totalAmount: (json['totalAmount'] as num).toDouble(),
      thisMonthOrders: json['thisMonthOrders'] as int,
      thisMonthAmount: (json['thisMonthAmount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalOrders': totalOrders,
      'completedOrders': completedOrders,
      'totalAmount': totalAmount,
      'thisMonthOrders': thisMonthOrders,
      'thisMonthAmount': thisMonthAmount,
    };
  }
}

/// 流程步骤
class ProcessStep {
  final int step;
  final String title;
  final String description;
  final String? icon;

  const ProcessStep({
    required this.step,
    required this.title,
    required this.description,
    this.icon,
  });

  factory ProcessStep.fromJson(Map<String, dynamic> json) {
    return ProcessStep(
      step: json['step'] as int,
      title: json['title'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'step': step,
      'title': title,
      'description': description,
      'icon': icon,
    };
  }
}
