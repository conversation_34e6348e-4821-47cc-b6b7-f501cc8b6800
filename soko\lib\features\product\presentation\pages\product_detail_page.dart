import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/presentation/widgets/custom_app_bar.dart';

/// 商品详情页面
class ProductDetailPage extends ConsumerWidget {
  final String productId;

  const ProductDetailPage({
    super.key,
    required this.productId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const CustomAppBar(title: '商品详情'),
      body: Center(
        child: Text('商品详情页面 - 待实现\nID: $productId'),
      ),
    );
  }
}
