import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/presentation/widgets/custom_app_bar.dart';

/// 订单列表页面
class OrderListPage extends ConsumerWidget {
  final String? orderType;

  const OrderListPage({
    super.key,
    this.orderType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String title = '我的订单';
    if (orderType != null) {
      switch (orderType) {
        case 'pending':
          title = '待付款';
          break;
        case 'shipping':
          title = '待收货';
          break;
        case 'completed':
          title = '已完成';
          break;
      }
    }

    return Scaffold(
      appBar: CustomAppBar(title: title),
      body: const Center(
        child: Text('订单列表页面 - 待实现'),
      ),
    );
  }
}
