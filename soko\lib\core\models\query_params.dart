import 'package:json_annotation/json_annotation.dart';

part 'query_params.g.dart';

/// 基础查询参数
@JsonSerializable()
class BaseQueryParams {
  @JsonKey(name: 'page')
  final int page;

  @JsonKey(name: 'size')
  final int size;

  const BaseQueryParams({
    required this.page,
    required this.size,
  });

  factory BaseQueryParams.fromJson(Map<String, dynamic> json) =>
      _$BaseQueryParamsFromJson(json);

  Map<String, dynamic> toJson() => _$BaseQueryParamsToJson(this);
}

/// 订单查询参数
@JsonSerializable()
class OrderQueryParams extends BaseQueryParams {
  @JsonKey(name: 'orderStatus')
  final String? orderStatus;

  @JsonKey(name: 'startDate')
  final String? startDate;

  @JsonKey(name: 'endDate')
  final String? endDate;

  @JsonKey(name: 'orderNo')
  final String? orderNo;

  const OrderQueryParams({
    required super.page,
    required super.size,
    this.orderStatus,
    this.startDate,
    this.endDate,
    this.orderNo,
  });

  factory OrderQueryParams.fromJson(Map<String, dynamic> json) =>
      _$OrderQueryParamsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$OrderQueryParamsToJson(this);
}

/// 回收订单查询参数
@JsonSerializable()
class RecycleOrderQueryParams extends BaseQueryParams {
  @JsonKey(name: 'orderStatus')
  final String? orderStatus;

  @JsonKey(name: 'productName')
  final String? productName;

  @JsonKey(name: 'startDate')
  final String? startDate;

  @JsonKey(name: 'endDate')
  final String? endDate;

  const RecycleOrderQueryParams({
    required super.page,
    required super.size,
    this.orderStatus,
    this.productName,
    this.startDate,
    this.endDate,
  });

  factory RecycleOrderQueryParams.fromJson(Map<String, dynamic> json) =>
      _$RecycleOrderQueryParamsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$RecycleOrderQueryParamsToJson(this);
}

/// 商品查询参数
@JsonSerializable()
class ProductQueryParams extends BaseQueryParams {
  @JsonKey(name: 'keyword')
  final String? keyword;

  @JsonKey(name: 'category')
  final String? category;

  @JsonKey(name: 'acg')
  final String? acg;

  @JsonKey(name: 'brand')
  final String? brand;

  @JsonKey(name: 'minPrice')
  final double? minPrice;

  @JsonKey(name: 'maxPrice')
  final double? maxPrice;

  @JsonKey(name: 'condition')
  final String? condition;

  @JsonKey(name: 'sortBy')
  final String? sortBy;

  @JsonKey(name: 'sortOrder')
  final String? sortOrder;

  const ProductQueryParams({
    required super.page,
    required super.size,
    this.keyword,
    this.category,
    this.acg,
    this.brand,
    this.minPrice,
    this.maxPrice,
    this.condition,
    this.sortBy,
    this.sortOrder,
  });

  factory ProductQueryParams.fromJson(Map<String, dynamic> json) =>
      _$ProductQueryParamsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$ProductQueryParamsToJson(this);
}

/// 优惠券查询参数
@JsonSerializable()
class CouponQueryParams extends BaseQueryParams {
  @JsonKey(name: 'type')
  final String? type;

  @JsonKey(name: 'status')
  final String? status;

  @JsonKey(name: 'available')
  final bool? available;

  const CouponQueryParams({
    required super.page,
    required super.size,
    this.type,
    this.status,
    this.available,
  });

  factory CouponQueryParams.fromJson(Map<String, dynamic> json) =>
      _$CouponQueryParamsFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$CouponQueryParamsToJson(this);
}

/// 搜索参数
@JsonSerializable()
class SearchParams {
  @JsonKey(name: 'keyword')
  final String keyword;

  @JsonKey(name: 'page')
  final int page;

  @JsonKey(name: 'size')
  final int size;

  @JsonKey(name: 'filters')
  final Map<String, dynamic>? filters;

  @JsonKey(name: 'sortBy')
  final String? sortBy;

  @JsonKey(name: 'sortOrder')
  final String? sortOrder;

  const SearchParams({
    required this.keyword,
    required this.page,
    required this.size,
    this.filters,
    this.sortBy,
    this.sortOrder,
  });

  factory SearchParams.fromJson(Map<String, dynamic> json) =>
      _$SearchParamsFromJson(json);

  Map<String, dynamic> toJson() => _$SearchParamsToJson(this);
}

/// 筛选参数
@JsonSerializable()
class FilterParams {
  @JsonKey(name: 'categories')
  final List<String>? categories;

  @JsonKey(name: 'brands')
  final List<String>? brands;

  @JsonKey(name: 'priceRange')
  final PriceRange? priceRange;

  @JsonKey(name: 'conditions')
  final List<String>? conditions;

  @JsonKey(name: 'locations')
  final List<String>? locations;

  const FilterParams({
    this.categories,
    this.brands,
    this.priceRange,
    this.conditions,
    this.locations,
  });

  factory FilterParams.fromJson(Map<String, dynamic> json) =>
      _$FilterParamsFromJson(json);

  Map<String, dynamic> toJson() => _$FilterParamsToJson(this);
}

/// 价格区间
@JsonSerializable()
class PriceRange {
  @JsonKey(name: 'min')
  final double? min;

  @JsonKey(name: 'max')
  final double? max;

  const PriceRange({
    this.min,
    this.max,
  });

  factory PriceRange.fromJson(Map<String, dynamic> json) =>
      _$PriceRangeFromJson(json);

  Map<String, dynamic> toJson() => _$PriceRangeToJson(this);

  /// 是否有效的价格区间
  bool get isValid {
    if (min == null && max == null) return false;
    if (min != null && max != null) return min! <= max!;
    return true;
  }
}

/// 排序参数
@JsonSerializable()
class SortParams {
  @JsonKey(name: 'field')
  final String field;

  @JsonKey(name: 'order')
  final String order; // 'asc' or 'desc'

  const SortParams({
    required this.field,
    required this.order,
  });

  factory SortParams.fromJson(Map<String, dynamic> json) =>
      _$SortParamsFromJson(json);

  Map<String, dynamic> toJson() => _$SortParamsToJson(this);

  /// 是否为升序
  bool get isAscending => order.toLowerCase() == 'asc';

  /// 是否为降序
  bool get isDescending => order.toLowerCase() == 'desc';
}
