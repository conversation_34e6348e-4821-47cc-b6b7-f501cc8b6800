import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/utils/validator_utils.dart';
import '../../../../shared/presentation/widgets/custom_button.dart';
import '../../../../shared/presentation/widgets/custom_text_field.dart';
import '../providers/auth_provider.dart';

/// 修改密码页面
class ChangePasswordPage extends ConsumerStatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  ConsumerState<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends ConsumerState<ChangePasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          '修改密码',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 20,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                const Text(
                  '修改密码',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '为了您的账户安全，请定期修改密码',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 32),

                // 当前密码输入框
                PasswordTextField(
                  controller: _oldPasswordController,
                  label: '当前密码',
                  hintText: '请输入当前密码',
                ),
                const SizedBox(height: 16),

                // 新密码输入框
                PasswordTextField(
                  controller: _newPasswordController,
                  label: '新密码',
                  hintText: '请输入新密码（6-20位）',
                ),
                const SizedBox(height: 16),

                // 确认新密码输入框
                PasswordTextField(
                  controller: _confirmPasswordController,
                  label: '确认新密码',
                  hintText: '请再次输入新密码',
                ),
                const SizedBox(height: 32),

                // 修改密码按钮
                Consumer(
                  builder: (context, ref, child) {
                    final passwordState = ref.watch(passwordProvider);
                    
                    return PrimaryButton(
                      text: '修改密码',
                      width: double.infinity,
                      isLoading: passwordState.isLoading,
                      onPressed: _handleChangePassword,
                    );
                  },
                ),
                const SizedBox(height: 24),

                // 密码安全提示
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            '密码安全提示',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '• 密码长度为6-20位\n'
                        '• 建议包含字母、数字和特殊字符\n'
                        '• 不要使用过于简单的密码\n'
                        '• 定期更换密码以保证账户安全',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理修改密码
  void _handleChangePassword() {
    if (!_validateForm()) return;

    ref.read(passwordProvider.notifier).changePassword(
      oldPassword: _oldPasswordController.text.trim(),
      newPassword: _newPasswordController.text.trim(),
    );

    // 监听修改结果
    ref.listen(passwordProvider, (previous, next) {
      next.when(
        idle: () {},
        loading: () {},
        success: (_) {
          // 修改成功
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('密码修改成功')),
          );
          Navigator.of(context).pop();
        },
        error: (message) {
          // 修改失败
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message)),
          );
        },
      );
    });
  }

  /// 验证表单
  bool _validateForm() {
    final oldPassword = _oldPasswordController.text.trim();
    final newPassword = _newPasswordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    if (oldPassword.isEmpty) {
      _showError('请输入当前密码');
      return false;
    }

    if (newPassword.isEmpty) {
      _showError('请输入新密码');
      return false;
    }

    if (!ValidatorUtils.isValidPassword(newPassword)) {
      _showError('新密码长度应为6-20位');
      return false;
    }

    if (confirmPassword.isEmpty) {
      _showError('请确认新密码');
      return false;
    }

    if (newPassword != confirmPassword) {
      _showError('两次输入的新密码不一致');
      return false;
    }

    if (oldPassword == newPassword) {
      _showError('新密码不能与当前密码相同');
      return false;
    }

    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
