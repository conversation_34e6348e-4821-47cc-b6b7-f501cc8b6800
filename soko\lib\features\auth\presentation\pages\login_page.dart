import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/utils/validator_utils.dart';
import '../../../../shared/presentation/widgets/custom_button.dart';
import '../../../../shared/presentation/widgets/custom_text_field.dart';
import '../providers/auth_provider.dart';
import '../widgets/sms_code_button.dart';

/// 登录页面
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // 密码登录控制器
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();

  // 验证码登录控制器
  final _smsPhoneController = TextEditingController();
  final _smsCodeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _smsPhoneController.dispose();
    _smsCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                const Text(
                  '欢迎回来',
                  style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text(
                  '登录您的账户',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                const SizedBox(height: 40),

                // Tab栏
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    indicator: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    indicatorPadding: const EdgeInsets.all(4),
                    labelColor: Colors.black,
                    unselectedLabelColor: Colors.grey,
                    tabs: const [
                      Tab(text: '密码登录'),
                      Tab(text: '验证码登录'),
                    ],
                  ),
                ),
                const SizedBox(height: 32),

                // Tab内容
                SizedBox(
                  height: 300,
                  child: TabBarView(
                    controller: _tabController,
                    children: [_buildPasswordLogin(), _buildSmsLogin()],
                  ),
                ),

                const SizedBox(height: 24),

                // 注册链接
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('还没有账户？', style: TextStyle(color: Colors.grey)),
                    TextButton(
                      onPressed: () {
                        // 跳转到注册页面
                      },
                      child: const Text(
                        '立即注册',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 密码登录表单
  Widget _buildPasswordLogin() {
    return Column(
      children: [
        // 手机号输入框
        PhoneTextField(controller: _phoneController, hintText: '请输入手机号'),
        const SizedBox(height: 16),

        // 密码输入框
        PasswordTextField(controller: _passwordController, hintText: '请输入密码'),
        const SizedBox(height: 8),

        // 忘记密码
        Align(
          alignment: Alignment.centerRight,
          child: TextButton(
            onPressed: () {
              // 跳转到忘记密码页面
            },
            child: const Text('忘记密码？', style: TextStyle(fontSize: 12)),
          ),
        ),
        const SizedBox(height: 24),

        // 登录按钮
        PrimaryButton(
          text: '登录',
          width: double.infinity,
          onPressed: _handlePasswordLogin,
        ),
      ],
    );
  }

  /// 验证码登录表单
  Widget _buildSmsLogin() {
    return Column(
      children: [
        // 手机号输入框
        PhoneTextField(controller: _smsPhoneController, hintText: '请输入手机号'),
        const SizedBox(height: 16),

        // 验证码输入框
        Row(
          children: [
            Expanded(
              child: CodeTextField(
                controller: _smsCodeController,
                hintText: '请输入验证码',
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(onPressed: _sendSmsCode, child: const Text('获取验证码')),
          ],
        ),
        const SizedBox(height: 32),

        // 登录按钮
        PrimaryButton(
          text: '登录',
          width: double.infinity,
          onPressed: _handleSmsLogin,
        ),
      ],
    );
  }

  /// 处理密码登录
  void _handlePasswordLogin() {
    if (!_validatePasswordForm()) return;

    // 这里调用登录API
    print('密码登录: ${_phoneController.text}, ${_passwordController.text}');
  }

  /// 处理验证码登录
  void _handleSmsLogin() {
    if (!_validateSmsForm()) return;

    // 这里调用验证码登录API
    print('验证码登录: ${_smsPhoneController.text}, ${_smsCodeController.text}');
  }

  /// 发送短信验证码
  void _sendSmsCode() {
    final phone = _smsPhoneController.text.trim();
    if (phone.isEmpty || !ValidatorUtils.isValidPhone(phone)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入正确的手机号')));
      return;
    }

    // 这里调用发送验证码API
    print('发送验证码: $phone');
  }

  /// 验证密码登录表单
  bool _validatePasswordForm() {
    final phone = _phoneController.text.trim();
    final password = _passwordController.text.trim();

    if (phone.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入手机号')));
      return false;
    }

    if (!ValidatorUtils.isValidPhone(phone)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入正确的手机号')));
      return false;
    }

    if (password.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入密码')));
      return false;
    }

    return true;
  }

  /// 验证验证码登录表单
  bool _validateSmsForm() {
    final phone = _smsPhoneController.text.trim();
    final smsCode = _smsCodeController.text.trim();

    if (phone.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入手机号')));
      return false;
    }

    if (!ValidatorUtils.isValidPhone(phone)) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入正确的手机号')));
      return false;
    }

    if (smsCode.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入验证码')));
      return false;
    }

    return true;
  }
}
