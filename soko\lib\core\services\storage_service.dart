import 'dart:convert';

import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';

/// 本地存储服务
class StorageService {
  static late SharedPreferences _prefs;
  static late Box _box;
  
  // 私有构造函数
  StorageService._();
  
  /// 初始化存储服务
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _box = await Hive.openBox('soko_storage');
  }
  
  /// 保存字符串
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }
  
  /// 获取字符串
  static String? getString(String key, {String? defaultValue}) {
    return _prefs.getString(key) ?? defaultValue;
  }
  
  /// 保存整数
  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }
  
  /// 获取整数
  static int? getInt(String key, {int? defaultValue}) {
    return _prefs.getInt(key) ?? defaultValue;
  }
  
  /// 保存布尔值
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }
  
  /// 获取布尔值
  static bool? getBool(String key, {bool? defaultValue}) {
    return _prefs.getBool(key) ?? defaultValue;
  }
  
  /// 保存双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }
  
  /// 获取双精度浮点数
  static double? getDouble(String key, {double? defaultValue}) {
    return _prefs.getDouble(key) ?? defaultValue;
  }
  
  /// 保存对象（JSON序列化）
  static Future<bool> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    return await _prefs.setString(key, jsonString);
  }
  
  /// 获取对象（JSON反序列化）
  static Map<String, dynamic>? getObject(String key) {
    final jsonString = _prefs.getString(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
  
  /// 删除指定键的数据
  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
  
  /// 清空所有数据
  static Future<bool> clear() async {
    return await _prefs.clear();
  }
  
  /// 检查是否包含指定键
  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }
  
  /// 获取所有键
  static Set<String> getKeys() {
    return _prefs.getKeys();
  }
  
  // Hive存储方法（用于复杂数据）
  
  /// 保存到Hive
  static Future<void> putHive(String key, dynamic value) async {
    await _box.put(key, value);
  }
  
  /// 从Hive获取
  static T? getHive<T>(String key, {T? defaultValue}) {
    return _box.get(key, defaultValue: defaultValue) as T?;
  }
  
  /// 从Hive删除
  static Future<void> deleteHive(String key) async {
    await _box.delete(key);
  }
  
  /// 清空Hive
  static Future<void> clearHive() async {
    await _box.clear();
  }
  
  // 业务相关的便捷方法
  
  /// 保存用户Token
  static Future<bool> saveToken(String token) async {
    return await setString(AppConfig.tokenKey, token);
  }
  
  /// 获取用户Token
  static String? getToken() {
    return getString(AppConfig.tokenKey);
  }
  
  /// 删除用户Token
  static Future<bool> removeToken() async {
    return await remove(AppConfig.tokenKey);
  }
  
  /// 保存用户信息
  static Future<bool> saveUserInfo(Map<String, dynamic> userInfo) async {
    return await setObject(AppConfig.userInfoKey, userInfo);
  }
  
  /// 获取用户信息
  static Map<String, dynamic>? getUserInfo() {
    return getObject(AppConfig.userInfoKey);
  }
  
  /// 删除用户信息
  static Future<bool> removeUserInfo() async {
    return await remove(AppConfig.userInfoKey);
  }
  
  /// 检查是否已登录
  static bool isLoggedIn() {
    final token = getToken();
    return token != null && token.isNotEmpty;
  }
  
  /// 退出登录（清除用户相关数据）
  static Future<void> logout() async {
    await removeToken();
    await removeUserInfo();
    // 可以添加其他需要清除的用户数据
  }
}
