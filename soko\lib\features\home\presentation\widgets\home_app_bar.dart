import 'package:flutter/material.dart';

/// 首页AppBar组件
class HomeAppBar extends StatelessWidget {
  final VoidCallback? onSearchTap;
  final VoidCallback? onNotificationTap;
  final VoidCallback? onScanTap;

  const HomeAppBar({
    super.key,
    this.onSearchTap,
    this.onNotificationTap,
    this.onScanTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            // Logo和标题
            Expanded(
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        '中',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '中古虾',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            
            // 搜索按钮
            IconButton(
              onPressed: onSearchTap ?? () {
                // 跳转到搜索页面
              },
              icon: const Icon(
                Icons.search,
                color: Colors.black54,
                size: 24,
              ),
            ),
            
            // 扫码按钮
            IconButton(
              onPressed: onScanTap ?? () {
                // 打开扫码功能
              },
              icon: const Icon(
                Icons.qr_code_scanner,
                color: Colors.black54,
                size: 24,
              ),
            ),
            
            // 消息通知按钮
            Stack(
              children: [
                IconButton(
                  onPressed: onNotificationTap ?? () {
                    // 跳转到消息中心
                  },
                  icon: const Icon(
                    Icons.notifications_outlined,
                    color: Colors.black54,
                    size: 24,
                  ),
                ),
                // 红点提示
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 搜索栏AppBar组件
class SearchAppBar extends StatelessWidget {
  final String? hintText;
  final VoidCallback? onTap;
  final VoidCallback? onScanTap;
  final VoidCallback? onNotificationTap;

  const SearchAppBar({
    super.key,
    this.hintText,
    this.onTap,
    this.onScanTap,
    this.onNotificationTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            // 搜索框
            Expanded(
              child: GestureDetector(
                onTap: onTap,
                child: Container(
                  height: 36,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.search,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        hintText ?? '搜索商品',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 扫码按钮
            GestureDetector(
              onTap: onScanTap,
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Icon(
                  Icons.qr_code_scanner,
                  color: Colors.grey[600],
                  size: 20,
                ),
              ),
            ),
            
            const SizedBox(width: 8),
            
            // 消息通知按钮
            Stack(
              children: [
                IconButton(
                  onPressed: onNotificationTap,
                  icon: Icon(
                    Icons.notifications_outlined,
                    color: Colors.grey[600],
                    size: 24,
                  ),
                ),
                // 红点提示
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 透明AppBar组件
class TransparentAppBar extends StatelessWidget {
  final Color? backgroundColor;
  final VoidCallback? onBackTap;
  final List<Widget>? actions;

  const TransparentAppBar({
    super.key,
    this.backgroundColor,
    this.onBackTap,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor ?? Colors.transparent,
      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            // 返回按钮
            IconButton(
              onPressed: onBackTap ?? () => Navigator.of(context).pop(),
              icon: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Colors.white,
                  size: 18,
                ),
              ),
            ),
            
            const Spacer(),
            
            // 操作按钮
            if (actions != null) ...actions!,
          ],
        ),
      ),
    );
  }
}
