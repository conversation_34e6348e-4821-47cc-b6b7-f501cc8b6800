import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/presentation/widgets/custom_app_bar.dart';

/// 创建回收订单页面
class CreateRecycleOrderPage extends ConsumerWidget {
  const CreateRecycleOrderPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const CustomAppBar(title: '创建回收订单'),
      body: const Center(
        child: Text('创建回收订单页面 - 待实现'),
      ),
    );
  }
}
