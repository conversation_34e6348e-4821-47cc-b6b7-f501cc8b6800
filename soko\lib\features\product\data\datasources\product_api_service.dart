import '../../../../core/api/base_api_service.dart';
import '../../../../core/models/query_params.dart';
import '../../../../core/network/api_response.dart';
import '../../domain/entities/product.dart';

/// 商品API服务
class ProductApiService extends BaseApiService {
  static const String _productListPath = '/product/list';
  static const String _productSearchPath = '/product/search';
  static const String _productDetailPath = '/product';
  static const String _productNewPath = '/product/list/new';
  static const String _productHotPath = '/product/list/hot';
  static const String _productByAcgPath = '/product/list/acg';
  static const String _categoryTreePath = '/category/tree';
  static const String _bannersPath = '/banner/list';

  /// 获取商品列表
  Future<ApiResponse<PageResponse<Product>>> getProductList({
    required ProductQueryParams params,
  }) async {
    final queryParams = buildQueryParams({
      'page': params.page,
      'size': params.size,
      'category': params.category,
      'acg': params.acg,
      'brand': params.brand,
      'minPrice': params.minPrice,
      'maxPrice': params.maxPrice,
      'condition': params.condition,
      'sortBy': params.sortBy,
      'sortOrder': params.sortOrder,
    });

    final response = await get<Map<String, dynamic>>(
      _productListPath,
      queryParameters: queryParams,
    );

    if (response.isSuccess && response.data != null) {
      final pageData = handlePageResponse<Product>(
        response.data!,
        (json) => Product.fromJson(json),
      );
      
      return ApiResponse<PageResponse<Product>>(
        code: response.code,
        message: response.message,
        data: pageData,
        timestamp: response.timestamp,
      );
    }

    return ApiResponse<PageResponse<Product>>(
      code: response.code,
      message: response.message,
      data: null,
      timestamp: response.timestamp,
    );
  }

  /// 搜索商品
  Future<ApiResponse<PageResponse<Product>>> searchProducts({
    required SearchParams params,
  }) async {
    return await post<PageResponse<Product>>(
      _productSearchPath,
      data: params.toJson(),
      fromJson: (data) => handlePageResponse<Product>(
        data as Map<String, dynamic>,
        (json) => Product.fromJson(json),
      ),
    );
  }

  /// 获取商品详情
  Future<ApiResponse<Product>> getProductDetail({
    required String productId,
  }) async {
    return await get<Product>(
      '$_productDetailPath/$productId',
      fromJson: (data) => Product.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 获取新品列表
  Future<ApiResponse<List<Product>>> getNewProducts({
    int limit = 10,
  }) async {
    return await get<List<Product>>(
      _productNewPath,
      queryParameters: {'limit': limit},
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => Product.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 获取热门商品列表
  Future<ApiResponse<List<Product>>> getHotProducts({
    int limit = 10,
  }) async {
    return await get<List<Product>>(
      _productHotPath,
      queryParameters: {'limit': limit},
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => Product.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 根据ACG分类获取商品
  Future<ApiResponse<PageResponse<Product>>> getProductsByAcg({
    required String acg,
    int page = 1,
    int size = 20,
  }) async {
    final response = await post<Map<String, dynamic>>(
      _productByAcgPath,
      data: {
        'acg': acg,
        'page': page,
        'size': size,
      },
    );

    if (response.isSuccess && response.data != null) {
      final pageData = handlePageResponse<Product>(
        response.data!,
        (json) => Product.fromJson(json),
      );
      
      return ApiResponse<PageResponse<Product>>(
        code: response.code,
        message: response.message,
        data: pageData,
        timestamp: response.timestamp,
      );
    }

    return ApiResponse<PageResponse<Product>>(
      code: response.code,
      message: response.message,
      data: null,
      timestamp: response.timestamp,
    );
  }

  /// 获取分类树
  Future<ApiResponse<List<CategoryNode>>> getCategoryTree({
    String? code,
  }) async {
    return await get<List<CategoryNode>>(
      _categoryTreePath,
      queryParameters: code != null ? {'code': code} : null,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => CategoryNode.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 获取ACG分类
  Future<ApiResponse<List<CategoryNode>>> getAcgCategories() async {
    return await getCategoryTree(code: 'ACG');
  }

  /// 获取轮播图
  Future<ApiResponse<List<Banner>>> getBanners() async {
    return await get<List<Banner>>(
      _bannersPath,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => Banner.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 收藏商品
  Future<ApiResponse<void>> favoriteProduct({
    required String productId,
  }) async {
    return await post<void>(
      '/product/favorite',
      data: {'productId': productId},
    );
  }

  /// 取消收藏商品
  Future<ApiResponse<void>> unfavoriteProduct({
    required String productId,
  }) async {
    return await delete<void>(
      '/product/favorite',
      data: {'productId': productId},
    );
  }

  /// 获取收藏商品列表
  Future<ApiResponse<PageResponse<Product>>> getFavoriteProducts({
    int page = 1,
    int size = 20,
  }) async {
    final response = await get<Map<String, dynamic>>(
      '/product/favorite/list',
      queryParameters: {
        'page': page,
        'size': size,
      },
    );

    if (response.isSuccess && response.data != null) {
      final pageData = handlePageResponse<Product>(
        response.data!,
        (json) => Product.fromJson(json),
      );
      
      return ApiResponse<PageResponse<Product>>(
        code: response.code,
        message: response.message,
        data: pageData,
        timestamp: response.timestamp,
      );
    }

    return ApiResponse<PageResponse<Product>>(
      code: response.code,
      message: response.message,
      data: null,
      timestamp: response.timestamp,
    );
  }
}

/// 分类节点
class CategoryNode {
  final String id;
  final String name;
  final String? code;
  final String? icon;
  final int sort;
  final List<CategoryNode>? children;

  const CategoryNode({
    required this.id,
    required this.name,
    this.code,
    this.icon,
    required this.sort,
    this.children,
  });

  factory CategoryNode.fromJson(Map<String, dynamic> json) {
    return CategoryNode(
      id: json['id'] as String,
      name: json['name'] as String,
      code: json['code'] as String?,
      icon: json['icon'] as String?,
      sort: json['sort'] as int? ?? 0,
      children: (json['children'] as List<dynamic>?)
          ?.map((item) => CategoryNode.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'icon': icon,
      'sort': sort,
      'children': children?.map((item) => item.toJson()).toList(),
    };
  }
}

/// 轮播图
class Banner {
  final String id;
  final String title;
  final String? description;
  final String imageUrl;
  final String? linkUrl;
  final String? linkType;
  final int sort;
  final bool enabled;

  const Banner({
    required this.id,
    required this.title,
    this.description,
    required this.imageUrl,
    this.linkUrl,
    this.linkType,
    required this.sort,
    required this.enabled,
  });

  factory Banner.fromJson(Map<String, dynamic> json) {
    return Banner(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['imageUrl'] as String,
      linkUrl: json['linkUrl'] as String?,
      linkType: json['linkType'] as String?,
      sort: json['sort'] as int? ?? 0,
      enabled: json['enabled'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'linkUrl': linkUrl,
      'linkType': linkType,
      'sort': sort,
      'enabled': enabled,
    };
  }
}
