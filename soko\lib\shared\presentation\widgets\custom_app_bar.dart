import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// 自定义AppBar
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;

  const CustomAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.showBackButton = true,
    this.onBackPressed,
    this.actions,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ?? (title != null ? Text(title!) : null),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.white,
      foregroundColor: foregroundColor ?? AppColors.textPrimary,
      elevation: elevation ?? 0,
      scrolledUnderElevation: 0,
      titleTextStyle: AppTextStyles.titleLarge.copyWith(
        color: foregroundColor ?? AppColors.textPrimary,
        fontWeight: FontWeight.w600,
      ),
      leading: showBackButton
          ? IconButton(
              onPressed: onBackPressed ?? () => context.pop(),
              icon: Icon(
                Icons.arrow_back_ios,
                size: 20.w,
                color: foregroundColor ?? AppColors.textPrimary,
              ),
            )
          : null,
      actions: actions,
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(0.5.h),
        child: Container(
          height: 0.5.h,
          color: AppColors.divider,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 0.5.h);
}
