import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../exceptions/network_exception.dart';

/// 错误处理拦截器
class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    NetworkException networkException;

    switch (err.type) {
      case DioExceptionType.connectionTimeout:
        networkException = NetworkException(
          message: '连接超时，请检查网络连接',
          code: 'CONNECTION_TIMEOUT',
          statusCode: null,
        );
        break;

      case DioExceptionType.sendTimeout:
        networkException = NetworkException(
          message: '请求发送超时',
          code: 'SEND_TIMEOUT',
          statusCode: null,
        );
        break;

      case DioExceptionType.receiveTimeout:
        networkException = NetworkException(
          message: '响应接收超时',
          code: 'RECEIVE_TIMEOUT',
          statusCode: null,
        );
        break;

      case DioExceptionType.badResponse:
        networkException = _handleResponseError(err);
        break;

      case DioExceptionType.cancel:
        networkException = NetworkException(
          message: '请求已取消',
          code: 'REQUEST_CANCELLED',
          statusCode: null,
        );
        break;

      case DioExceptionType.connectionError:
        networkException = NetworkException(
          message: '网络连接失败，请检查网络设置',
          code: 'CONNECTION_ERROR',
          statusCode: null,
        );
        break;

      case DioExceptionType.badCertificate:
        networkException = NetworkException(
          message: 'SSL证书验证失败',
          code: 'BAD_CERTIFICATE',
          statusCode: null,
        );
        break;

      case DioExceptionType.unknown:
      default:
        networkException = NetworkException(
          message: '未知网络错误：${err.message}',
          code: 'UNKNOWN_ERROR',
          statusCode: null,
        );
        break;
    }

    // 在调试模式下打印详细错误信息
    if (kDebugMode) {
      print('🔴 Network Error: ${networkException.toString()}');
      print('🔴 Original Error: ${err.toString()}');
    }

    // 将DioException转换为自定义的NetworkException
    final customError = DioException(
      requestOptions: err.requestOptions,
      error: networkException,
      type: err.type,
      response: err.response,
    );

    handler.next(customError);
  }

  /// 处理HTTP响应错误
  NetworkException _handleResponseError(DioException err) {
    final statusCode = err.response?.statusCode;
    final responseData = err.response?.data;

    String message;
    String code;

    // 尝试从响应中获取错误信息
    if (responseData is Map<String, dynamic>) {
      message = responseData['message'] ?? responseData['msg'] ?? _getDefaultMessage(statusCode);
      code = responseData['code']?.toString() ?? statusCode.toString();
    } else {
      message = _getDefaultMessage(statusCode);
      code = statusCode.toString();
    }

    return NetworkException(
      message: message,
      code: code,
      statusCode: statusCode,
      data: responseData,
    );
  }

  /// 根据状态码获取默认错误消息
  String _getDefaultMessage(int? statusCode) {
    switch (statusCode) {
      case 400:
        return '请求参数错误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '禁止访问';
      case 404:
        return '请求的资源不存在';
      case 405:
        return '请求方法不允许';
      case 408:
        return '请求超时';
      case 409:
        return '请求冲突';
      case 422:
        return '请求参数验证失败';
      case 429:
        return '请求过于频繁，请稍后再试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂时不可用';
      case 504:
        return '网关超时';
      default:
        return '网络请求失败（$statusCode）';
    }
  }
}
