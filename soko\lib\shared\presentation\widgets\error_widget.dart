import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

/// 错误展示组件
class ErrorDisplayWidget extends StatelessWidget {
  final String message;
  final String? title;
  final VoidCallback? onRetry;
  final Widget? icon;
  final bool showRetryButton;

  const ErrorDisplayWidget({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.icon,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 错误图标
            icon ??
                Icon(
                  Icons.error_outline,
                  size: 64.w,
                  color: AppColors.error,
                ),

            SizedBox(height: 16.h),

            // 错误标题
            if (title != null) ...[
              Text(
                title!,
                style: AppTextStyles.titleLarge.copyWith(
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
            ],

            // 错误消息
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),

            // 重试按钮
            if (showRetryButton && onRetry != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('重试'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 网络错误组件
class NetworkErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      title: '网络连接失败',
      message: '请检查网络设置后重试',
      icon: Icon(
        Icons.wifi_off,
        size: 64.w,
        color: AppColors.error,
      ),
      onRetry: onRetry,
    );
  }
}

/// 空数据组件
class EmptyDataWidget extends StatelessWidget {
  final String message;
  final String? title;
  final Widget? icon;
  final VoidCallback? onRefresh;

  const EmptyDataWidget({
    super.key,
    this.message = '暂无数据',
    this.title,
    this.icon,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 空数据图标
            icon ??
                Icon(
                  Icons.inbox_outlined,
                  size: 64.w,
                  color: AppColors.textTertiary,
                ),

            SizedBox(height: 16.h),

            // 标题
            if (title != null) ...[
              Text(
                title!,
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
            ],

            // 消息
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),

            // 刷新按钮
            if (onRefresh != null) ...[
              SizedBox(height: 24.h),
              TextButton(
                onPressed: onRefresh,
                child: const Text('刷新'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 加载失败组件
class LoadFailedWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const LoadFailedWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      title: '加载失败',
      message: message ?? '数据加载失败，请重试',
      icon: Icon(
        Icons.refresh,
        size: 64.w,
        color: AppColors.error,
      ),
      onRetry: onRetry,
    );
  }
}

/// 服务器错误组件
class ServerErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const ServerErrorWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      title: '服务器繁忙',
      message: '服务器暂时无法响应，请稍后重试',
      icon: Icon(
        Icons.cloud_off,
        size: 64.w,
        color: AppColors.error,
      ),
      onRetry: onRetry,
    );
  }
}

/// 权限错误组件
class PermissionErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onAction;
  final String? actionText;

  const PermissionErrorWidget({
    super.key,
    this.message,
    this.onAction,
    this.actionText,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      title: '权限不足',
      message: message ?? '您没有权限访问此内容',
      icon: Icon(
        Icons.lock_outline,
        size: 64.w,
        color: AppColors.warning,
      ),
      onRetry: onAction,
      showRetryButton: onAction != null,
    );
  }
}
