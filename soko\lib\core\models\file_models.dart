import 'package:json_annotation/json_annotation.dart';

import '../enums/app_enums.dart';

part 'file_models.g.dart';

/// 文件信息
@JsonSerializable()
class FileInfo {
  @Json<PERSON>ey(name: 'id')
  final String id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'fileName')
  final String fileName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'originalName')
  final String? originalName;

  @J<PERSON><PERSON><PERSON>(name: 'url')
  final String url;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnailUrl')
  final String? thumbnailUrl;

  @J<PERSON><PERSON><PERSON>(name: 'fileSize')
  final int fileSize;

  @Json<PERSON>ey(name: 'fileType')
  final String fileType;

  @Json<PERSON><PERSON>(name: 'mimeType')
  final String? mimeType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'width')
  final int? width;

  @Json<PERSON>ey(name: 'height')
  final int? height;

  @<PERSON><PERSON><PERSON>ey(name: 'duration')
  final int? duration;

  @Json<PERSON>ey(name: 'createTime')
  final int createTime;

  const FileInfo({
    required this.id,
    required this.fileName,
    this.originalName,
    required this.url,
    this.thumbnailUrl,
    required this.fileSize,
    required this.fileType,
    this.mimeType,
    this.width,
    this.height,
    this.duration,
    required this.createTime,
  });

  factory FileInfo.fromJson(Map<String, dynamic> json) => _$FileInfoFromJson(json);

  Map<String, dynamic> toJson() => _$FileInfoToJson(this);

  /// 获取文件类型枚举
  FileType get fileTypeEnum {
    switch (fileType.toLowerCase()) {
      case 'image':
        return FileType.image;
      case 'video':
        return FileType.video;
      case 'audio':
        return FileType.audio;
      case 'document':
        return FileType.document;
      default:
        return FileType.other;
    }
  }

  /// 获取格式化的文件大小
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 是否为图片
  bool get isImage => fileTypeEnum == FileType.image;

  /// 是否为视频
  bool get isVideo => fileTypeEnum == FileType.video;

  /// 是否为音频
  bool get isAudio => fileTypeEnum == FileType.audio;

  /// 是否为文档
  bool get isDocument => fileTypeEnum == FileType.document;

  /// 获取显示用的URL（优先使用缩略图）
  String get displayUrl => thumbnailUrl ?? url;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FileInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FileInfo(id: $id, fileName: $fileName, fileSize: $formattedFileSize)';
  }
}

/// 文件上传请求
@JsonSerializable()
class FileUploadRequest {
  @JsonKey(name: 'fileName')
  final String fileName;

  @JsonKey(name: 'fileType')
  final String fileType;

  @JsonKey(name: 'fileSize')
  final int fileSize;

  @JsonKey(name: 'mimeType')
  final String? mimeType;

  @JsonKey(name: 'category')
  final String? category;

  const FileUploadRequest({
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    this.mimeType,
    this.category,
  });

  factory FileUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$FileUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$FileUploadRequestToJson(this);
}

/// 文件上传响应
@JsonSerializable()
class FileUploadResponse {
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'url')
  final String url;

  @JsonKey(name: 'thumbnailUrl')
  final String? thumbnailUrl;

  @JsonKey(name: 'uploadUrl')
  final String? uploadUrl;

  @JsonKey(name: 'uploadToken')
  final String? uploadToken;

  const FileUploadResponse({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    this.uploadUrl,
    this.uploadToken,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FileUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FileUploadResponseToJson(this);
}

/// 上传进度信息
class UploadProgress {
  final String fileId;
  final String fileName;
  final int totalBytes;
  final int uploadedBytes;
  final UploadStatus status;
  final String? error;

  const UploadProgress({
    required this.fileId,
    required this.fileName,
    required this.totalBytes,
    required this.uploadedBytes,
    required this.status,
    this.error,
  });

  /// 上传进度百分比
  double get progress {
    if (totalBytes == 0) return 0;
    return uploadedBytes / totalBytes;
  }

  /// 上传进度百分比（0-100）
  int get progressPercent {
    return (progress * 100).round();
  }

  /// 是否上传完成
  bool get isCompleted => status == UploadStatus.success;

  /// 是否上传失败
  bool get isFailed => status == UploadStatus.failed;

  /// 是否正在上传
  bool get isUploading => status == UploadStatus.uploading;

  /// 复制并更新上传进度
  UploadProgress copyWith({
    String? fileId,
    String? fileName,
    int? totalBytes,
    int? uploadedBytes,
    UploadStatus? status,
    String? error,
  }) {
    return UploadProgress(
      fileId: fileId ?? this.fileId,
      fileName: fileName ?? this.fileName,
      totalBytes: totalBytes ?? this.totalBytes,
      uploadedBytes: uploadedBytes ?? this.uploadedBytes,
      status: status ?? this.status,
      error: error ?? this.error,
    );
  }

  @override
  String toString() {
    return 'UploadProgress(fileId: $fileId, progress: ${progressPercent}%, status: $status)';
  }
}

/// 图片项（用于回收订单等场景）
@JsonSerializable()
class ImageItem {
  @JsonKey(name: 'fileId')
  final String fileId;

  @JsonKey(name: 'url')
  final String url;

  @JsonKey(name: 'thumbnailUrl')
  final String? thumbnailUrl;

  const ImageItem({
    required this.fileId,
    required this.url,
    this.thumbnailUrl,
  });

  factory ImageItem.fromJson(Map<String, dynamic> json) => _$ImageItemFromJson(json);

  Map<String, dynamic> toJson() => _$ImageItemToJson(this);

  /// 获取显示用的URL
  String get displayUrl => thumbnailUrl ?? url;
}

/// 分类项（用于回收分类等场景）
@JsonSerializable()
class CategoryItem {
  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'value')
  final String value;

  @JsonKey(name: 'icon')
  final String? icon;

  @JsonKey(name: 'description')
  final String? description;

  const CategoryItem({
    required this.name,
    required this.value,
    this.icon,
    this.description,
  });

  factory CategoryItem.fromJson(Map<String, dynamic> json) => _$CategoryItemFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryItemToJson(this);
}

/// 条件选项（用于商品成色等场景）
@JsonSerializable()
class ConditionOption {
  @JsonKey(name: 'value')
  final String value;

  @JsonKey(name: 'label')
  final String label;

  @JsonKey(name: 'desc')
  final String? desc;

  @JsonKey(name: 'color')
  final String? color;

  const ConditionOption({
    required this.value,
    required this.label,
    this.desc,
    this.color,
  });

  factory ConditionOption.fromJson(Map<String, dynamic> json) => _$ConditionOptionFromJson(json);

  Map<String, dynamic> toJson() => _$ConditionOptionToJson(this);
}
