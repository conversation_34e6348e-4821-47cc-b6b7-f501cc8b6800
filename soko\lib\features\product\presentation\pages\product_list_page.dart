import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/presentation/widgets/custom_app_bar.dart';

/// 商品列表页面
class ProductListPage extends ConsumerWidget {
  final String? categoryId;
  final String? keyword;

  const ProductListPage({
    super.key,
    this.categoryId,
    this.keyword,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String title = '商品列表';
    if (keyword != null) {
      title = '搜索: $keyword';
    } else if (categoryId != null) {
      title = '分类商品';
    }

    return Scaffold(
      appBar: CustomAppBar(title: title),
      body: const Center(
        child: Text('商品列表页面 - 待实现'),
      ),
    );
  }
}
