import 'package:flutter/material.dart';

import '../../../../shared/presentation/widgets/custom_image.dart';
import '../../../product/data/datasources/product_api_service.dart';

/// 轮播图组件
class BannerCarousel extends StatefulWidget {
  final List<Banner> banners;
  final double height;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;

  const BannerCarousel({
    super.key,
    required this.banners,
    this.height = 180,
    this.margin,
    this.borderRadius,
  });

  @override
  State<BannerCarousel> createState() => _BannerCarouselState();
}

class _BannerCarouselState extends State<BannerCarousel> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // 自动轮播
    if (widget.banners.length > 1) {
      _startAutoPlay();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 开始自动轮播
  void _startAutoPlay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && widget.banners.length > 1) {
        final nextIndex = (_currentIndex + 1) % widget.banners.length;
        _pageController.animateToPage(
          nextIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoPlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.banners.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: widget.height,
      margin: widget.margin ?? const EdgeInsets.all(16),
      child: Stack(
        children: [
          // 轮播图
          ClipRRect(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.banners.length,
              itemBuilder: (context, index) {
                final banner = widget.banners[index];
                return GestureDetector(
                  onTap: () => _onBannerTap(banner),
                  child: CustomImage(
                    imageUrl: banner.imageUrl,
                    width: double.infinity,
                    height: widget.height,
                    fit: BoxFit.cover,
                  ),
                );
              },
            ),
          ),
          
          // 指示器
          if (widget.banners.length > 1)
            Positioned(
              bottom: 12,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: widget.banners.asMap().entries.map((entry) {
                  final index = entry.key;
                  final isActive = index == _currentIndex;
                  
                  return Container(
                    width: isActive ? 20 : 6,
                    height: 6,
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    decoration: BoxDecoration(
                      color: isActive 
                          ? Colors.white 
                          : Colors.white.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  /// 处理轮播图点击
  void _onBannerTap(Banner banner) {
    if (banner.linkUrl != null && banner.linkUrl!.isNotEmpty) {
      // 根据linkType处理不同的跳转逻辑
      switch (banner.linkType) {
        case 'product':
          // 跳转到商品详情
          break;
        case 'category':
          // 跳转到分类页面
          break;
        case 'url':
          // 打开外部链接
          break;
        default:
          // 默认处理
          break;
      }
    }
  }
}

/// 简单轮播图组件（用于占位）
class SimpleBannerCarousel extends StatelessWidget {
  final double height;
  final EdgeInsetsGeometry? margin;
  final String? placeholder;

  const SimpleBannerCarousel({
    super.key,
    this.height = 180,
    this.margin,
    this.placeholder,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      margin: margin ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              placeholder ?? '轮播图区域',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
